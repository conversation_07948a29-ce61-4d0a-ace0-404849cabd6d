// save as run_comfy_flux.js
// usage: `node run_comfy_flux.js`
// edit the config below to change prompt, negative, size and seed

const fs = require("fs");
const path = require("path");
const crypto = require("crypto");

const config = {
  comfyUrl: "http://127.0.0.1:8188",
  workflowPath: "./flux1_experiment.json", // put your JSON here
  prompt:
    "Floral simple green 4k wallpaper",
  negativePrompt:
    "blurry, low quality, distortion, realistic",
  width: 3840,
  height: 2160,
  seed: -1 // -1 means random
};

function randomSeed() {
  // safe 32-bit random
  return crypto.randomInt(0, 2 ** 32 - 1);
}

function sleep(ms) {
  return new Promise(res => setTimeout(res, ms));
}

async function main() {
  const scriptDir = __dirname;
  if (!fs.existsSync(config.workflowPath)) {
    throw new Error(`Workflow file not found: ${config.workflowPath}`);
  }

  const raw = fs.readFileSync(config.workflowPath, "utf-8");
  const graph = JSON.parse(raw);

  // update width/height on EmptySD3LatentImage node "27"
  if (graph["27"] && graph["27"].class_type === "EmptySD3LatentImage") {
    graph["27"].inputs.width = config.width;
    graph["27"].inputs.height = config.height;
  } else {
    console.warn("Warning: node 27 not found or not EmptySD3LatentImage");
  }

  // update seed on KSampler node "31"
  const seedValue = config.seed === -1 ? randomSeed() : Number(config.seed);
  if (graph["31"] && graph["31"].class_type === "KSampler") {
    graph["31"].inputs.seed = seedValue;
  } else {
    console.warn("Warning: node 31 not found or not KSampler");
  }

  // update prompts on CLIPTextEncode nodes "45" (positive) and "52" (negative)
  if (graph["45"] && graph["45"].class_type === "CLIPTextEncode") {
    graph["45"].inputs.text = config.prompt;
  } else {
    console.warn("Warning: node 45 not found or not CLIPTextEncode");
  }
  if (graph["52"] && graph["52"].class_type === "CLIPTextEncode") {
    graph["52"].inputs.text = config.negativePrompt;
  } else {
    console.warn("Warning: node 52 not found or not CLIPTextEncode");
  }

  // optional: give SaveImage a unique prefix so it is easy to spot in history
  const saveNodeKey = Object.keys(graph).find(
    k => graph[k].class_type === "SaveImage"
  );
  const uniquePrefix = `api_${Date.now()}`;
  if (saveNodeKey) {
    graph[saveNodeKey].inputs.filename_prefix = uniquePrefix;
  }

  const client_id = crypto.randomBytes(8).toString("hex");

  // send prompt
  const pRes = await fetch(`${config.comfyUrl}/prompt`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ prompt: graph, client_id })
  });

  if (!pRes.ok) {
    const text = await pRes.text().catch(() => "");
    throw new Error(`ComfyUI /prompt failed: ${pRes.status} ${text}`);
  }

  const { prompt_id } = await pRes.json();

  // poll history until we get images
  const started = Date.now();
  let images = null;

  while (Date.now() - started < 5 * 60 * 1000) { // wait up to 5 min
    const hRes = await fetch(`${config.comfyUrl}/history/${prompt_id}`);
    if (hRes.ok) {
      const hist = await hRes.json();
      const item = hist[prompt_id];
      if (item && item.outputs) {
        // find first node output that has images
        for (const nodeKey of Object.keys(item.outputs)) {
          const out = item.outputs[nodeKey];
          if (out.images && out.images.length > 0) {
            images = out.images;
            break;
          }
        }
        if (images) break;
      }
    }
    await sleep(1000);
  }

  if (!images || images.length === 0) {
    throw new Error("No images found in history for this prompt");
  }

  // take first image and download it
  const img = images[0];
  const url =
    `${config.comfyUrl}/view?filename=` +
    encodeURIComponent(img.filename) +
    `&type=${encodeURIComponent(img.type || "output")}` +
    `&subfolder=${encodeURIComponent(img.subfolder || "")}`;

  const iRes = await fetch(url);
  if (!iRes.ok) {
    const text = await iRes.text().catch(() => "");
    throw new Error(`Failed to fetch image: ${iRes.status} ${text}`);
  }

  const arrayBuf = await iRes.arrayBuffer();
  const buffer = Buffer.from(arrayBuf);

  const outName = `${Date.now()}.png`;
  const outPath = path.join(scriptDir, outName);
  fs.writeFileSync(outPath, buffer);

  console.log(`Saved: ${outPath}`);
  console.log(`Seed used: ${seedValue}`);
}

main().catch(err => {
  console.error(err);
  process.exit(1);
});

